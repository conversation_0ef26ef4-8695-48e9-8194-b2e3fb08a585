@import "tailwindcss";

:root {
  /* Core Theme Colors */
  --brand-purple: #8A2BE2; /* Brighter Purple - Primary */
  --brand-yellow: #FFD700; /* Gold/Yellow - Accent */
  --brand-black: #000000; /* Black - Background/Contrast */

  /* Text Colors */
  --text-primary: #FFFFFF; /* White for primary text */
  --text-secondary: #D1D5DB; /* Light Gray for secondary text */
  --text-muted: #9CA3AF; /* Medium Gray for muted text */
  --text-on-yellow: #000000; /* Black text for yellow backgrounds for contrast */
  --text-on-purple: #FFFFFF; /* White text for purple backgrounds */

  /* Background Colors */
  --background: var(--brand-black);
  --foreground: var(--text-primary);
  --surface: #111827; /* Very dark gray (gray-900), slightly lighter than black */
  --surface-dark: #0D1117; /* Even darker gray, close to black */
  
  /* UI Element Colors */
  --border: #4B0082; /* Darker Purple for borders */
  --accent: var(--brand-yellow); /* Yellow as the main accent */
  --link-color: var(--brand-yellow);
  --link-hover-color: #FFEE00; /* Brighter yellow for link hover */

  /* Functional Colors */
  --error-color: #F87171; /* Red-400 */
  --success-color: #34D399; /* Green-400 */
  --warning-color: #FBBF24; /* Amber-400 */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-primary: var(--brand-purple);
  --color-secondary: var(--brand-yellow);
  --color-accent: var(--brand-yellow);
  --color-muted: var(--text-muted);
  --color-border: var(--border);
}

/* Performance optimizations */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus styles for accessibility */
:focus-visible {
  outline: 2px solid var(--brand-purple);
  outline-offset: 2px;
}

/* Skip to main content link for screen readers */
.skip-to-main {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--brand-purple);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-to-main:focus {
  top: 6px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--brand-purple);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--brand-yellow);
}

/* Selection styles */
::selection {
  background: var(--brand-purple);
  color: white;
}

::-moz-selection {
  background: var(--brand-purple);
  color: white;
}

/* Performance: Reduce paint on transforms */
.transform-gpu {
  transform: translateZ(0);
  will-change: transform;
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(139, 92, 246, 0.1),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* You might want a slightly off-black for the main body if pure black is too stark */
body {
  background: var(--background); /* Or a slightly lighter black like #121212 */
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
