export default function TestimonialsPage() {
  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      company: "Tech Solutions Inc.",
      quote: "Working with [Your Name] was an absolute pleasure. Their attention to detail and creative vision transformed our raw footage into a stunning promotional video that exceeded all our expectations. Highly recommended!",
      avatar: "/placeholder-headshot.svg" // Replace with actual path or use a generic icon
    },
    {
      id: 2,
      name: "<PERSON>",
      company: "Indie Film Productions",
      quote: "The editing on our short film was masterful. [Your Name] has a real knack for storytelling and pacing. The final cut was emotionally resonant and beautifully crafted. We're thrilled with the result.",
      avatar: "/placeholder-headshot.svg"
    },
    {
      id: 3,
      name: "<PERSON>",
      company: "Music Artist Collective",
      quote: "Our music video looks incredible thanks to [Your Name]'s editing skills. They brought so much energy and style to the project, perfectly capturing the vibe of the song. A true professional!",
      avatar: "/placeholder-headshot.svg"
    }
  ];

  return (
    <div className="container mx-auto px-4 py-16">
      <h1 className="text-4xl font-bold mb-12 text-center">Client Testimonials</h1>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {testimonials.map((testimonial) => (
          <div key={testimonial.id} className="bg-surface p-6 rounded-lg shadow-xl flex flex-col">
            <p className="text-text-secondary italic mb-6 flex-grow">"{testimonial.quote}"</p>
            <div className="flex items-center mt-auto">
              {/* <img src={testimonial.avatar} alt={testimonial.name} className="w-12 h-12 rounded-full mr-4 object-cover" /> */}
              <div className="w-12 h-12 rounded-full mr-4 bg-surface-dark flex items-center justify-center text-text-on-purple font-bold">
                {testimonial.name.substring(0,1)}{testimonial.name.split(' ')[1]?.substring(0,1) || ''}
              </div>
              <div>
                <p className="font-semibold text-text-primary">{testimonial.name}</p>
                <p className="text-sm text-text-muted">{testimonial.company}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}