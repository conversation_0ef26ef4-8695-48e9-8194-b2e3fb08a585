'use client';

import React from 'react';
import { cn } from '@/lib/utils';

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, variant = 'default', size = 'md', children, ...props }, ref) => {
    const baseStyles = "inline-flex items-center font-medium transition-all duration-200";
    
    const variants = {
      default: "bg-surface text-text-secondary hover:bg-surface-dark",
      primary: "bg-brand-primary/20 text-brand-primary border border-brand-primary/30 hover:bg-brand-primary/30",
      secondary: "bg-brand-secondary/20 text-brand-secondary border border-brand-secondary/30 hover:bg-brand-secondary/30",
      success: "bg-success-500/20 text-success-500 border border-success-500/30 hover:bg-success-500/30",
      warning: "bg-warning-500/20 text-warning-500 border border-warning-500/30 hover:bg-warning-500/30",
      error: "bg-error-500/20 text-error-500 border border-error-500/30 hover:bg-error-500/30",
      outline: "border border-border text-text-secondary hover:bg-surface"
    };

    const sizes = {
      sm: "px-2 py-1 text-xs rounded-md",
      md: "px-3 py-1 text-sm rounded-md",
      lg: "px-4 py-2 text-base rounded-lg"
    };

    return (
      <div
        className={cn(
          baseStyles,
          variants[variant],
          sizes[size],
          className
        )}
        ref={ref}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Badge.displayName = "Badge";

export default Badge;
