import React, { useRef, Suspense, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';

interface BoxProps {
  position: [number, number, number];
}

const Box = (props: BoxProps) => {
  const meshRef = useRef<THREE.Mesh>(null);
  
  useFrame((state, delta) => {
    if (meshRef.current) {
      // Smoother animation with delta time
      meshRef.current.rotation.x += delta * 0.5;
      meshRef.current.rotation.y += delta * 0.3;
    }
  });

  return (
    <mesh {...props} ref={meshRef}>
      <boxGeometry args={[1, 1, 1]} />
      <meshStandardMaterial 
        color="var(--brand-purple)" 
        metalness={0.3}
        roughness={0.4}
      />
    </mesh>
  );
};

const Scene = () => {
  return (
    <>
      <ambientLight intensity={0.5} />
      <pointLight position={[10, 10, 10]} intensity={0.8} />
      <Box position={[0, 0, 0]} />
      <OrbitControls 
        enableZoom={false}
        enablePan={false}
        autoRotate
        autoRotateSpeed={0.5}
      />
    </>
  );
};

const ThreeDElement = () => {
  // Memoize canvas settings for performance
  const canvasSettings = useMemo(() => ({
    camera: { position: [3, 3, 3], fov: 60 },
    gl: { 
      antialias: false, // Disable for better performance
      alpha: true,
      powerPreference: "high-performance" as const
    },
    dpr: Math.min(window.devicePixelRatio, 2), // Limit pixel ratio for performance
    performance: { min: 0.5 } // Adaptive performance
  }), []);

  return (
    <div className="w-full h-full">
      <Canvas {...canvasSettings}>
        <Suspense fallback={null}>
          <Scene />
        </Suspense>
      </Canvas>
    </div>
  );
};

export default ThreeDElement;