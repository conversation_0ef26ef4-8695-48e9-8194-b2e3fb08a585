'use client';

import Link from 'next/link';
import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import { usePathname } from 'next/navigation';
import { Menu, X } from 'lucide-react';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const headerRef = useRef<HTMLElement>(null);
  const logoRef = useRef<HTMLDivElement>(null);
  const ctaRef = useRef<HTMLButtonElement>(null);
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  // GSAP Animations
  useEffect(() => {
    // Initial entrance animation for the header
    gsap.fromTo(
      headerRef.current,
      { y: -100, opacity: 0 },
      { y: 0, opacity: 1, duration: 1, ease: "power3.out" }
    );

    // Mobile menu animation
    if (mobileMenuRef.current) {
      gsap.set(mobileMenuRef.current, { xPercent: 100 }); // Start off-screen
    }
  }, []);

  useEffect(() => {
    if (mobileMenuRef.current) {
      if (isMobileMenuOpen) {
        gsap.to(mobileMenuRef.current, {
          xPercent: 0,
          duration: 0.5,
          ease: "power3.inOut",
        });
        document.body.style.overflow = "hidden"; // Prevent scrolling when mobile menu is open
      } else {
        gsap.to(mobileMenuRef.current, {
          xPercent: 100,
          duration: 0.5,
          ease: "power3.inOut",
        });
        document.body.style.overflow = "auto";
      }
    }
  }, [isMobileMenuOpen]);

  // Scroll detection
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Hover effects for logo and CTA
  useEffect(() => {
    const logoEl = logoRef.current;
    const ctaEl = ctaRef.current;

    if (logoEl) {
      logoEl.addEventListener("mouseenter", () =>
        gsap.to(logoEl, { scale: 1.1, color: "var(--brand-yellow)", duration: 0.3 })
      );
      logoEl.addEventListener("mouseleave", () =>
        gsap.to(logoEl, { scale: 1, color: "var(--text-primary)", duration: 0.3 })
      );
    }
    if (ctaEl) {
      ctaEl.addEventListener("mouseenter", () =>
        gsap.to(ctaEl, { backgroundColor: "var(--brand-yellow)", color: "var(--text-on-yellow)", scale: 1.05, duration: 0.3 })
      );
      ctaEl.addEventListener("mouseleave", () =>
        gsap.to(ctaEl, { backgroundColor: "var(--brand-purple)", color: "var(--text-on-purple)", scale: 1, duration: 0.3 })
      );
    }

    return () => {
      if (logoEl) {
        logoEl.removeEventListener("mouseenter", () => {}); // Clean up, though empty functions are fine here
        logoEl.removeEventListener("mouseleave", () => {});
      }
      if (ctaEl) {
        ctaEl.removeEventListener("mouseenter", () => {});
        ctaEl.removeEventListener("mouseleave", () => {});
      }
    };
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const navLinks = [
    { href: "/", label: "Home" },
    { href: "/services", label: "Services" },
    { href: "/portfolio", label: "Portfolio" },
    { href: "/about", label: "About" },
    { href: "/contact", label: "Contact" },
  ];

  return (
    <header
      ref={headerRef}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ease-in-out 
                  ${isScrolled ? "bg-surface/80 backdrop-blur-md shadow-lg" : "bg-transparent"}`}
      style={{ willChange: "background-color, box-shadow" }} // Performance hint
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 flex items-center justify-between h-16 md:h-20">
        <div
          ref={logoRef}
          className="text-2xl md:text-3xl font-bold cursor-pointer transition-colors duration-300"
          style={{ color: 'var(--text-primary)' }} // Initial color set via style for GSAP
        >
          <Link href="/">Jackson Abetianbe</Link>
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex space-x-6 items-center">
          {navLinks.map((link) => (
            <Link key={link.href} href={link.href} className="text-text-secondary hover:text-brand-yellow transition-colors duration-300">
              {link.label}
            </Link>
          ))}
          <button
            ref={ctaRef}
            className="px-6 py-2 rounded-full font-semibold transition-all duration-300 shadow-md hover:shadow-lg"
            style={{
              backgroundColor: 'var(--brand-purple)', 
              color: 'var(--text-on-purple)',
              willChange: 'background-color, color, transform' // Performance hint
            }}
          >
            Get a Quote
          </button>
        </nav>

        {/* Mobile Menu Button */}
        <div className="md:hidden">
          <button
            onClick={toggleMobileMenu}
            className="text-text-primary focus:outline-none"
            aria-label="Toggle menu"
          >
            {isMobileMenuOpen ? (
              <X size={28} className="text-brand-yellow" />
            ) : (
              <Menu size={28} className="text-brand-yellow" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Menu Panel */}
      <div
        ref={mobileMenuRef}
        className="md:hidden fixed inset-0 bg-surface/95 backdrop-blur-lg z-40 transform translate-x-full"
        style={{ willChange: 'transform' }} // Performance hint
      >
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-20 flex flex-col items-center space-y-6">
          {navLinks.map((link) => (
            <Link
              key={link.href}
              href={link.href}
              className="text-2xl text-text-primary hover:text-brand-yellow transition-colors duration-300"
              onClick={toggleMobileMenu} // Close menu on link click
            >
              {link.label}
            </Link>
          ))}
          <button
            className="mt-6 px-8 py-3 rounded-full bg-brand-purple text-text-on-purple font-semibold text-lg transition-colors duration-300 hover:bg-brand-yellow hover:text-text-on-yellow shadow-md hover:shadow-lg"
            style={{ willChange: 'background-color, color' }} // Performance hint
          >
            Get a Quote
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;