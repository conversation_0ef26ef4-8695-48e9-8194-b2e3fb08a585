import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        primary: "var(--brand-purple)",
        secondary: "var(--brand-yellow)",
        accent: "var(--accent)",
        muted: "var(--muted)",
        border: "var(--border)",
        'brand-purple': "var(--brand-purple)",
        'brand-yellow': "var(--brand-yellow)",
        'brand-black': "var(--brand-black)",
        'text-primary': "var(--text-primary)",
        'text-secondary': "var(--text-secondary)",
        'text-on-yellow': "var(--text-on-yellow)",
        'text-on-purple': "var(--text-on-purple)",
        'link-color': "var(--link-color)",
        'link-hover-color': "var(--link-hover-color)",
        'error-color': "var(--error-color)",
        'success-color': "var(--success-color)",
        'warning-color': "var(--warning-color)",
        surface: "var(--surface)",
      },
      fontFamily: {
        sans: ["var(--font-geist-sans)"],
        mono: ["var(--font-geist-mono)"],
      },
      // ... any other theme extensions
    },
  },
  plugins: [],
};
export default config;