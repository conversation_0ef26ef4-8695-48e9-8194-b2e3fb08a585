import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import ClientLayout from "./ClientLayout";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: 'swap',
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: 'swap',
});

export const metadata: Metadata = {
  title: {
    default: "<PERSON> - Professional Video Editing Services",
    template: "%s | <PERSON> Video Editing"
  },
  description: "Professional video editing services by <PERSON>. Creative excellence in color grading, motion graphics, and cinematic storytelling to bring your vision to life.",
  keywords: ["video editing", "Jackson <PERSON>", "professional video editor", "color grading", "motion graphics", "cinematic editing", "post-production", "video production", "freelance video editor"],
  authors: [{ name: "<PERSON>" }],
  creator: "<PERSON>",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://jacksonabetianbe.com", // Placeholder URL, update if you have a domain
    title: "Jackson Abetianbe - Professional Video Editing Services",
    description: "Creative video editing services by Jackson Abetianbe. Transforming visions into stunning visual stories.",
    siteName: "Jackson Abetianbe Video Editing",
    images: [
      {
        url: "/og-image-ja.jpg", // Consider creating a new OG image with the new branding
        width: 1200,
        height: 630,
        alt: "Jackson Abetianbe - Professional Video Editing",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Jackson Abetianbe - Professional Video Editing Services",
    description: "Creative video editing by Jackson Abetianbe. Color grading, motion graphics, and more.",
    images: ["/og-image-ja.jpg"], // Use the new OG image
    // creator: "@YourTwitterHandle", // Optional: Add Twitter handle if available
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html
      lang="en"
      suppressHydrationWarning
    >
      <head>
        <link rel="icon" href="/favicon-ja.ico" sizes="any" />
        <link rel="icon" href="/icon-ja.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon-ja.png" />
        <meta name="theme-color" content="#8A2BE2" /> {/* Updated to brand-purple */}
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-background text-foreground overflow-x-hidden`}
      >
        <ClientLayout>
          <Header />
          <main className="min-h-screen relative" role="main">
            {children}
          </main>
          <Footer />
        </ClientLayout>
      </body>
    </html>
  );
}
