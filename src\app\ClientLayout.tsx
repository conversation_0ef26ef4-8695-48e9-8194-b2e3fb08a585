'use client';

import { useEffect, useRef, useState } from 'react';
import { usePathname } from 'next/navigation';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import dynamic from 'next/dynamic';
import ErrorBoundary from '../components/ErrorBoundary';

// Dynamically import ThreeDElement for better performance
const ThreeDElement = dynamic(() => import('../components/ThreeDElement'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full bg-gradient-to-br from-purple-900/10 to-black/10 animate-pulse" />
  )
});

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

interface ClientLayoutProps {
  children: React.ReactNode;
}

const ClientLayout = ({ children }: ClientLayoutProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const loadingRef = useRef<HTMLDivElement>(null);
  const curtainRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [show3D, setShow3D] = useState(false);
  const pathname = usePathname();
  const prevPathnameRef = useRef(pathname);

  // Delay 3D element loading for better initial performance
  useEffect(() => {
    const timer = setTimeout(() => setShow3D(true), 2000);
    return () => clearTimeout(timer);
  }, []);

  // Initial loading animation
  useEffect(() => {
    const ctx = gsap.context(() => {
      const tl = gsap.timeline({
        onComplete: () => {
          setIsLoading(false);
        }
      });

      // Loading screen animation
      tl.set(loadingRef.current, { display: 'flex' })
        .fromTo(loadingRef.current?.querySelector('.loading-logo'), 
          { scale: 0, rotation: -180, opacity: 0 },
          { scale: 1, rotation: 0, opacity: 1, duration: 1, ease: "back.out(1.7)" }
        )
        .fromTo(loadingRef.current?.querySelector('.loading-text'), 
          { y: 50, opacity: 0 },
          { y: 0, opacity: 1, duration: 0.8, ease: "power3.out" },
          "-=0.5"
        )
        .fromTo(loadingRef.current?.querySelector('.loading-bar'), 
          { scaleX: 0 },
          { scaleX: 1, duration: 1.5, ease: "power2.inOut" },
          "-=0.3"
        )
        .to(loadingRef.current, 
          { opacity: 0, duration: 0.5, ease: "power2.inOut" },
          "+=0.5"
        )
        .set(loadingRef.current, { display: 'none' })
        .fromTo(containerRef.current, 
          { opacity: 0, y: 30 },
          { opacity: 1, y: 0, duration: 0.8, ease: "power3.out" }
        );

    }, containerRef);

    return () => ctx.revert();
  }, []);

  // Page transition animation
  useEffect(() => {
    if (prevPathnameRef.current !== pathname && !isLoading) {
      setIsTransitioning(true);
      
      const ctx = gsap.context(() => {
        const tl = gsap.timeline({
          onComplete: () => {
            setIsTransitioning(false);
            prevPathnameRef.current = pathname;
          }
        });

        // Page transition curtain effect
        tl.set(curtainRef.current, { display: 'block', scaleY: 0, transformOrigin: 'top' })
          .to(curtainRef.current, 
            { scaleY: 1, duration: 0.4, ease: "power2.inOut" }
          )
          .fromTo(containerRef.current, 
            { opacity: 1 },
            { opacity: 0, duration: 0.2 }
          )
          .set(containerRef.current, { opacity: 1 })
          .to(curtainRef.current, 
            { scaleY: 0, transformOrigin: 'bottom', duration: 0.4, ease: "power2.inOut" },
            "+=0.1"
          )
          .set(curtainRef.current, { display: 'none' });

      }, containerRef);

      return () => ctx.revert();
    }
  }, [pathname, isLoading]);

  // Smooth scrolling and cursor effects
  useEffect(() => {
    const ctx = gsap.context(() => {
      // Custom cursor
      const cursor = document.createElement('div');
      cursor.className = 'custom-cursor';
      cursor.style.cssText = `
        position: fixed;
        width: 20px;
        height: 20px;
        background: linear-gradient(45deg, #800080, #FFD700);
        border-radius: 50%;
        pointer-events: none;
        z-index: 9999;
        mix-blend-mode: difference;
        transition: transform 0.1s ease;
      `;
      document.body.appendChild(cursor);

      const cursorFollower = document.createElement('div');
      cursorFollower.className = 'cursor-follower';
      cursorFollower.style.cssText = `
        position: fixed;
        width: 40px;
        height: 40px;
        border: 2px solid rgba(255, 215, 0, 0.3);
        border-radius: 50%;
        pointer-events: none;
        z-index: 9998;
        transition: transform 0.2s ease;
      `;
      document.body.appendChild(cursorFollower);

      const handleMouseMove = (e: MouseEvent) => {
        // Use transform for better performance
        cursor.style.transform = `translate3d(${e.clientX - 10}px, ${e.clientY - 10}px, 0)`;
        
        // Smooth follower with requestAnimationFrame
        requestAnimationFrame(() => {
          cursorFollower.style.transform = `translate3d(${e.clientX - 20}px, ${e.clientY - 20}px, 0)`;
        });
      };

      const handleMouseEnter = () => {
        gsap.to([cursor, cursorFollower], {
          scale: 1.5,
          duration: 0.3,
          ease: "back.out(1.7)"
        });
      };

      const handleMouseLeave = () => {
        gsap.to([cursor, cursorFollower], {
          scale: 1,
          duration: 0.3,
          ease: "power2.out"
        });
      };

      document.addEventListener('mousemove', handleMouseMove);
      
      // Add hover effects to interactive elements
      const interactiveElements = document.querySelectorAll('a, button, [role="button"]');
      interactiveElements.forEach(el => {
        el.addEventListener('mouseenter', handleMouseEnter);
        el.addEventListener('mouseleave', handleMouseLeave);
      });

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        interactiveElements.forEach(el => {
          el.removeEventListener('mouseenter', handleMouseEnter);
          el.removeEventListener('mouseleave', handleMouseLeave);
        });
        cursor.remove();
        cursorFollower.remove();
      };
    });

    return () => ctx.revert();
  }, []);

  return (
    <>
      {/* Loading Screen */}
      {/* 3D Element Background */}
      {show3D && (
        <div className="fixed inset-0 z-0 opacity-20">
          <ErrorBoundary>
            <ThreeDElement />
          </ErrorBoundary>
        </div>
      )}

      {isLoading && (
        <div 
          ref={loadingRef}
          className="fixed inset-0 z-50 bg-gradient-to-br from-black via-purple-900 to-black flex flex-col items-center justify-center"
        >
          <div className="loading-logo text-6xl font-bold bg-gradient-to-r from-brand-yellow to-yellow-300 bg-clip-text text-transparent mb-8">
            Jackson Abetianbe Video Editing
          </div>
          <div className="loading-text text-xl text-text-secondary mb-8">
            Crafting Visual Excellence
          </div>
          <div className="w-64 h-1 bg-surface-dark rounded-full overflow-hidden">
            <div className="loading-bar h-full bg-gradient-to-r from-brand-purple to-brand-yellow rounded-full transform origin-left"></div>
          </div>
          <div className="mt-8 flex space-x-2">
            <div className="w-2 h-2 bg-brand-yellow rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
            <div className="w-2 h-2 bg-brand-purple rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
            <div className="w-2 h-2 bg-brand-yellow rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
          </div>
        </div>
      )}

      {/* Page Transition Curtain */}
      <div 
        ref={curtainRef}
        className="fixed inset-0 z-40 bg-gradient-to-br from-brand-purple to-black hidden"
        style={{ display: 'none' }}
      >
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-4xl font-bold bg-gradient-to-r from-brand-yellow to-yellow-300 bg-clip-text text-transparent">
            Loading...
          </div>
        </div>
      </div>

      {/* Main Content Container */}
      <div 
        ref={containerRef}
        className="relative z-10"
        style={{ opacity: isLoading ? 0 : 1 }}
      >
        {children}
      </div>

      {/* Background Particles */}
      <div className="fixed inset-0 pointer-events-none z-0">
        <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-brand-yellow rounded-full opacity-30 animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-0.5 h-0.5 bg-brand-purple rounded-full opacity-40 animate-ping"></div>
        <div className="absolute top-1/2 left-3/4 w-1.5 h-1.5 bg-brand-yellow rounded-full opacity-20 animate-bounce"></div>
        <div className="absolute bottom-1/4 left-1/2 w-0.5 h-0.5 bg-brand-purple rounded-full opacity-50 animate-pulse"></div>
      </div>
    </>
  );
};

export default ClientLayout;