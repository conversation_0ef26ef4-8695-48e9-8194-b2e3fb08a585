import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Header from '../Header';
import { usePathname } from 'next/navigation';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  usePathname: jest.fn()
}));

const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>;

describe('Header Component', () => {
  beforeEach(() => {
    mockUsePathname.mockReturnValue('/');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders the header with logo and navigation', () => {
    render(<Header />);
    
    // Check if logo is present
    expect(screen.getByText('Jackson Abetianbe')).toBeInTheDocument();
    
    // Check if navigation items are present
    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('About')).toBeInTheDocument();
    expect(screen.getByText('Services')).toBeInTheDocument();
    expect(screen.getByText('Portfolio')).toBeInTheDocument();
    expect(screen.getByText('Contact')).toBeInTheDocument();
    
    // Check if CTA button is present
    expect(screen.getByText('Get a Quote')).toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    render(<Header />);
    
    // Check for skip link
    expect(screen.getByText('Skip to main content')).toBeInTheDocument();
    
    // Check for proper ARIA labels
    expect(screen.getByRole('banner')).toBeInTheDocument();
    expect(screen.getByRole('navigation', { name: 'Main navigation' })).toBeInTheDocument();
    
    // Check logo accessibility
    const logo = screen.getByLabelText('Jackson Abetianbe - Go to homepage');
    expect(logo).toBeInTheDocument();
    
    // Check mobile menu button
    const mobileMenuButton = screen.getByLabelText('Toggle mobile menu');
    expect(mobileMenuButton).toBeInTheDocument();
    expect(mobileMenuButton).toHaveAttribute('aria-expanded', 'false');
  });

  it('highlights the active navigation item', () => {
    mockUsePathname.mockReturnValue('/about');
    render(<Header />);
    
    const aboutLink = screen.getByRole('menuitem', { name: 'Learn about us' });
    expect(aboutLink).toHaveAttribute('aria-current', 'page');
  });

  it('toggles mobile menu when button is clicked', async () => {
    const user = userEvent.setup();
    render(<Header />);
    
    const mobileMenuButton = screen.getByLabelText('Toggle mobile menu');
    
    // Initially closed
    expect(mobileMenuButton).toHaveAttribute('aria-expanded', 'false');
    expect(screen.queryByRole('menu')).not.toBeInTheDocument();
    
    // Click to open
    await user.click(mobileMenuButton);
    expect(mobileMenuButton).toHaveAttribute('aria-expanded', 'true');
    expect(screen.getByRole('menu')).toBeInTheDocument();
    
    // Click to close
    await user.click(mobileMenuButton);
    expect(mobileMenuButton).toHaveAttribute('aria-expanded', 'false');
  });

  it('closes mobile menu when clicking outside', async () => {
    const user = userEvent.setup();
    render(
      <div>
        <Header />
        <div data-testid="outside-element">Outside content</div>
      </div>
    );
    
    const mobileMenuButton = screen.getByLabelText('Toggle mobile menu');
    const outsideElement = screen.getByTestId('outside-element');
    
    // Open mobile menu
    await user.click(mobileMenuButton);
    expect(screen.getByRole('menu')).toBeInTheDocument();
    
    // Click outside
    fireEvent.mouseDown(outsideElement);
    
    await waitFor(() => {
      expect(mobileMenuButton).toHaveAttribute('aria-expanded', 'false');
    });
  });

  it('has proper keyboard navigation support', async () => {
    const user = userEvent.setup();
    render(<Header />);
    
    // Tab through navigation items
    await user.tab();
    expect(screen.getByText('Skip to main content')).toHaveFocus();
    
    await user.tab();
    expect(screen.getByLabelText('Jackson Abetianbe - Go to homepage')).toHaveFocus();
    
    // Continue tabbing through navigation items
    await user.tab();
    expect(screen.getByRole('menuitem', { name: 'Go to home page' })).toHaveFocus();
  });

  it('renders mobile menu items with proper accessibility', async () => {
    const user = userEvent.setup();
    render(<Header />);
    
    const mobileMenuButton = screen.getByLabelText('Toggle mobile menu');
    await user.click(mobileMenuButton);
    
    const mobileMenu = screen.getByRole('menu');
    expect(mobileMenu).toBeInTheDocument();
    
    // Check mobile menu items
    const mobileMenuItems = screen.getAllByRole('menuitem');
    expect(mobileMenuItems.length).toBeGreaterThan(5); // Desktop + mobile items
    
    // Check mobile CTA button
    const mobileCTA = screen.getAllByText('Get a Quote');
    expect(mobileCTA.length).toBe(2); // Desktop (hidden) + mobile
  });

  it('applies scroll effects correctly', () => {
    render(<Header />);
    
    // Simulate scroll event
    Object.defineProperty(window, 'scrollY', {
      writable: true,
      value: 100
    });
    
    fireEvent.scroll(window);
    
    // The header should have scroll-related classes applied
    const header = screen.getByRole('banner');
    expect(header).toHaveClass('sticky', 'top-0', 'z-50');
  });

  it('has proper focus management', async () => {
    const user = userEvent.setup();
    render(<Header />);
    
    // Test focus on logo
    const logo = screen.getByLabelText('Jackson Abetianbe - Go to homepage');
    await user.click(logo);
    
    // Test focus on navigation items
    const homeLink = screen.getByRole('menuitem', { name: 'Go to home page' });
    await user.click(homeLink);
    
    // Test focus on CTA button
    const ctaButton = screen.getByLabelText('Get a quote - Contact us for pricing');
    await user.click(ctaButton);
    
    // All interactive elements should be focusable
    expect(logo).toHaveAttribute('tabIndex');
    expect(homeLink).toHaveAttribute('tabIndex');
    expect(ctaButton).toHaveAttribute('tabIndex');
  });
});