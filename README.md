# Jackson Abetianbe Video Editing - Portfolio

A modern, accessible, and performant portfolio website for professional video editing services built with Next.js 15, React 18, and TypeScript.

## 🚀 Features

### Performance Optimizations
- **React 18 Compatibility**: Downgraded from React 19 to ensure compatibility with Three.js ecosystem
- **Optimized 3D Rendering**: Enhanced Three.js implementation with performance controls
- **Lazy Loading**: Dynamic imports for heavy components
- **Error Boundaries**: Graceful error handling for 3D elements
- **GPU Acceleration**: Transform optimizations for smooth animations

### Accessibility Improvements
- **WCAG 2.1 AA Compliant**: Comprehensive accessibility features
- **Keyboard Navigation**: Full keyboard support throughout the site
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Focus Management**: Visible focus indicators and logical tab order
- **Reduced Motion**: Respects user's motion preferences
- **Skip Links**: Quick navigation for assistive technologies

### SEO Enhancements
- **Comprehensive Meta Tags**: Open Graph, Twitter Cards, and structured data
- **Semantic HTML**: Proper heading hierarchy and landmark roles
- **Performance Metrics**: Optimized Core Web Vitals
- **Mobile-First Design**: Responsive and mobile-optimized

### Modern Development Stack
- **Next.js 15**: Latest features with App Router
- **TypeScript**: Full type safety
- **Tailwind CSS**: Utility-first styling with custom design tokens
- **GSAP**: Professional animations and interactions
- **React Three Fiber**: 3D graphics and animations
- **Jest & Testing Library**: Comprehensive testing suite

## 🛠️ Installation

### Prerequisites
- Node.js 18.x or higher
- npm or yarn package manager

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd jaeylo-video-editing-portfolio
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📝 Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report
- `npm run test:ci` - Run tests for CI/CD

## 🧪 Testing

The project includes a comprehensive testing suite with:

- **Unit Tests**: Component testing with React Testing Library
- **Integration Tests**: User interaction and accessibility testing
- **Coverage Reports**: Minimum 70% coverage threshold
- **Mocked Dependencies**: GSAP, Three.js, and Next.js router mocks

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

## 🎨 Design System

### Color Palette
- **Primary**: Black (#000000) - Main background
- **Secondary**: Purple (#8A2BE2) - Brand accent (Brighter Purple)
- **Accent**: Gold (#FFD700) - Highlights and CTAs
- **Surface**: Dark Gray (#111111) - Cards and surfaces
- **Text**: White (#FFFFFF) - Primary text

### Typography
- **Primary Font**: Geist Sans (system fallback)
- **Monospace**: Geist Mono
- **Line Height**: 1.6 for optimal readability

### Spacing & Layout
- **Container**: Max-width with responsive padding
- **Grid**: CSS Grid and Flexbox for layouts
- **Breakpoints**: Mobile-first responsive design

## 🔧 Architecture

### Project Structure
```
src/
├── app/                 # Next.js App Router
│   ├── globals.css     # Global styles and CSS variables
│   ├── layout.tsx      # Root layout with metadata
│   └── page.tsx        # Home page
├── components/         # Reusable components
│   ├── __tests__/      # Component tests
│   ├── ErrorBoundary.tsx
│   ├── Header.tsx      # Navigation with mobile support
│   ├── Footer.tsx      # Footer with social links
│   └── ThreeDElement.tsx # 3D background component
└── types/              # TypeScript type definitions
```

### Key Components

#### Header
- Responsive navigation with mobile menu
- Accessibility-first design
- Smooth scroll effects
- Active page indicators

#### Footer
- Contact information
- Social media links
- Legal pages navigation
- Animated background elements

#### ThreeDElement
- Optimized Three.js rendering
- Error boundary protection
- Performance controls
- Lazy loading

#### ErrorBoundary
- Graceful error handling
- User-friendly error messages
- Recovery mechanisms

## 🚀 Performance

### Optimizations Implemented
- **Code Splitting**: Dynamic imports for heavy components
- **Image Optimization**: Next.js Image component
- **Font Optimization**: System font stack with web font fallbacks
- **CSS Optimization**: Tailwind CSS purging and minification
- **JavaScript Optimization**: Tree shaking and minification
- **3D Performance**: Reduced polygon count and optimized materials

### Core Web Vitals
- **LCP**: Optimized with image preloading and critical CSS
- **FID**: Minimized JavaScript execution time
- **CLS**: Stable layouts with proper sizing

## ♿ Accessibility

### Features
- **Keyboard Navigation**: Full site navigation without mouse
- **Screen Reader Support**: Comprehensive ARIA labels
- **Focus Management**: Visible focus indicators
- **Color Contrast**: WCAG AA compliant contrast ratios
- **Motion Preferences**: Respects `prefers-reduced-motion`
- **Semantic HTML**: Proper heading hierarchy and landmarks

### Testing
Accessibility is tested using:
- Manual keyboard navigation
- Screen reader testing
- Automated accessibility testing in Jest
- Color contrast validation

## 🔒 Security

### Best Practices
- **No Hardcoded Secrets**: Environment variables for sensitive data
- **Secure Headers**: Next.js security headers
- **Input Validation**: TypeScript for type safety
- **Dependency Security**: Regular security audits

## 📱 Browser Support

- **Modern Browsers**: Chrome, Firefox, Safari, Edge (latest 2 versions)
- **Mobile**: iOS Safari, Chrome Mobile
- **Progressive Enhancement**: Graceful degradation for older browsers

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Code Style
- TypeScript for type safety
- ESLint for code quality
- Prettier for code formatting
- Conventional commits for commit messages

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Next.js Team** - For the amazing framework
- **Vercel** - For hosting and deployment
- **Three.js Community** - For 3D graphics capabilities
- **GSAP** - For smooth animations
- **Tailwind CSS** - For utility-first styling

## 📞 Support

For support, email <EMAIL> or create an issue in the repository.

---

**Jackson Abetianbe Video Editing** - Crafting cinematic experiences through professional video editing.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
